module.exports = {
  apps: [{
    name: 'xsender-whatsapp',
    script: 'app.js',
    cwd: '/home/<USER>/domains/mconnect.uniqsuporte.com.br/public_html/src',
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      NODE_SERVER_HOST: '127.0.0.1',
      NODE_SERVER_PORT: '3001',
      WA_CONNECTION_TIMEOUT: '30000',
      WA_RETRY_DELAY: '5000',
      WA_MAX_RECONNECT_ATTEMPTS: '3',
      DB_HOST: '127.0.0.1',
      DB_PORT: '3306',
      DB_DATABASE: 'mass10',
      DB_USERNAME: 'mass10',
      DB_PASSWORD: 'Fublpt66250522'
    },
    error_file: '/home/<USER>/domains/mconnect.uniqsuporte.com.br/public_html/src/storage/logs/pm2-error.log',
    out_file: '/home/<USER>/domains/mconnect.uniqsuporte.com.br/public_html/src/storage/logs/pm2-out.log',
    log_file: '/home/<USER>/domains/mconnect.uniqsuporte.com.br/public_html/src/storage/logs/pm2-combined.log',
    time: true,
    autorestart: true,
    restart_delay: 5000,
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    listen_timeout: 10000,
    node_args: '--max-old-space-size=1024',
    shutdown_with_message: true,
    wait_ready: true,
    health_check_grace_period: 3000,
    ignore_watch: [
      'node_modules',
      'sessions',
      'uploads',
      'storage/logs'
    ],
    env_production: {
      NODE_ENV: 'production',
      PORT: '3001'
    }
  }]
};
