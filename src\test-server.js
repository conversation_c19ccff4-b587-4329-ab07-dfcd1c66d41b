import express from 'express'
import cors from 'cors'

const app = express()
const port = 3008

app.use(cors())
app.use(express.json())

// Rota básica
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'WhatsApp Node Server is running',
        data: {
            status: 'online',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        }
    })
})

// Rota de status
app.get('/status', (req, res) => {
    res.json({
        success: true,
        message: 'Server status OK',
        data: {
            status: 'healthy',
            uptime: process.uptime(),
            memory: process.memoryUsage()
        }
    })
})

// Rota de init para sessions
app.post('/sessions/init', (req, res) => {
    res.json({
        success: true,
        message: 'Session init endpoint',
        data: {
            domain: req.body.domain || 'unknown'
        }
    })
})

// Catch all
app.all('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Method not allowed',
        data: {}
    })
})

app.listen(port, () => {
    console.log(`🚀 Test server listening on http://127.0.0.1:${port}`)
})
