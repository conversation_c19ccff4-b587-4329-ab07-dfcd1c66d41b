/**
 * SCRIPT DE CORREÇÃO AUTOMÁTICA PARA PRODUÇÃO
 * 
 * Aplica as correções necessárias para resolver:
 * - Request Timeout (408) no sessionValidator
 * - Dispositivos banidos não desconectando
 * - Problemas de comunicação Node.js ↔ <PERSON><PERSON>
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 APLICANDO CORREÇÕES PARA PRODUÇÃO');
console.log('=' .repeat(50));
console.log('📅 Data:', new Date().toLocaleString());
console.log('');

class CorrecaoProducao {
    constructor() {
        this.backupDir = path.join(__dirname, 'backup_' + Date.now());
        this.corrcoesAplicadas = [];
        this.erros = [];
    }

    log(emoji, mensagem) {
        console.log(`${emoji} ${mensagem}`);
    }

    erro(mensagem) {
        this.erros.push(mensagem);
        this.log('❌', mensagem);
    }

    sucesso(mensagem) {
        this.corrcoesAplicadas.push(mensagem);
        this.log('✅', mensagem);
    }

    // 1. CRIAR BACKUP DOS ARQUIVOS ORIGINAIS
    async criarBackup() {
        this.log('💾', 'Criando backup dos arquivos originais...');
        
        try {
            if (!fs.existsSync(this.backupDir)) {
                fs.mkdirSync(this.backupDir, { recursive: true });
            }

            const arquivosParaBackup = [
                'middleware/sessionValidator.js',
                'package.json',
                '.env'
            ];

            for (const arquivo of arquivosParaBackup) {
                const origem = path.join(__dirname, arquivo);
                const destino = path.join(this.backupDir, arquivo);
                
                if (fs.existsSync(origem)) {
                    // Criar diretório de destino se necessário
                    const destinoDir = path.dirname(destino);
                    if (!fs.existsSync(destinoDir)) {
                        fs.mkdirSync(destinoDir, { recursive: true });
                    }
                    
                    fs.copyFileSync(origem, destino);
                    this.sucesso(`Backup criado: ${arquivo}`);
                } else {
                    this.log('⚠️', `Arquivo não encontrado para backup: ${arquivo}`);
                }
            }
            
        } catch (error) {
            this.erro(`Erro ao criar backup: ${error.message}`);
        }
    }

    // 2. CORRIGIR SESSION VALIDATOR
    async corrigirSessionValidator() {
        this.log('🔧', 'Corrigindo sessionValidator.js...');
        
        const validatorPath = path.join(__dirname, 'middleware', 'sessionValidator.js');
        const validatorCorrigidoPath = path.join(__dirname, 'middleware', 'sessionValidator_corrigido.js');
        
        try {
            // Verificar se o arquivo corrigido existe
            if (!fs.existsSync(validatorCorrigidoPath)) {
                this.erro('Arquivo sessionValidator_corrigido.js não encontrado');
                return;
            }

            // Criar diretório middleware se não existir
            const middlewareDir = path.join(__dirname, 'middleware');
            if (!fs.existsSync(middlewareDir)) {
                fs.mkdirSync(middlewareDir, { recursive: true });
                this.sucesso('Diretório middleware criado');
            }

            // Copiar arquivo corrigido
            fs.copyFileSync(validatorCorrigidoPath, validatorPath);
            this.sucesso('sessionValidator.js corrigido com tratamento de timeout');
            
        } catch (error) {
            this.erro(`Erro ao corrigir sessionValidator: ${error.message}`);
        }
    }

    // 3. VERIFICAR E INSTALAR DEPENDÊNCIAS
    async verificarDependencias() {
        this.log('📦', 'Verificando dependências...');
        
        try {
            const packageJsonPath = path.join(__dirname, 'package.json');
            
            if (!fs.existsSync(packageJsonPath)) {
                this.erro('package.json não encontrado');
                return;
            }

            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            // Verificar se axios está instalado
            if (!packageJson.dependencies?.axios) {
                this.log('📦', 'Instalando axios...');
                await execAsync('npm install axios');
                this.sucesso('Axios instalado');
            } else {
                this.sucesso('Axios já está instalado');
            }
            
        } catch (error) {
            this.erro(`Erro ao verificar dependências: ${error.message}`);
        }
    }

    // 4. ATUALIZAR VARIÁVEIS DE AMBIENTE
    async atualizarEnv() {
        this.log('⚙️', 'Verificando variáveis de ambiente...');
        
        const envPath = path.join(__dirname, '.env');
        
        try {
            let envContent = '';
            
            if (fs.existsSync(envPath)) {
                envContent = fs.readFileSync(envPath, 'utf8');
            }

            let envAtualizado = false;

            // Verificar variáveis essenciais
            const variaveisEssenciais = [
                'LARAVEL_BASE_URL=http://localhost',
                'REQUEST_TIMEOUT=30000',
                'RETRY_ATTEMPTS=3',
                'RETRY_DELAY=2000'
            ];

            for (const variavel of variaveisEssenciais) {
                const [chave] = variavel.split('=');
                
                if (!envContent.includes(chave)) {
                    envContent += `\n${variavel}`;
                    envAtualizado = true;
                    this.sucesso(`Variável ${chave} adicionada ao .env`);
                }
            }

            if (envAtualizado) {
                fs.writeFileSync(envPath, envContent);
                this.sucesso('.env atualizado');
            } else {
                this.sucesso('.env já está configurado');
            }
            
        } catch (error) {
            this.erro(`Erro ao atualizar .env: ${error.message}`);
        }
    }

    // 5. LIMPAR SESSÕES ÓRFÃS
    async limparSessoesOrfas() {
        this.log('🗑️', 'Limpando sessões órfãs...');
        
        const sessoesPath = path.join(__dirname, 'sessions');
        
        try {
            if (!fs.existsSync(sessoesPath)) {
                this.log('⚠️', 'Diretório sessions não encontrado');
                return;
            }

            const files = fs.readdirSync(sessoesPath);
            const sessionDirs = files.filter(file => 
                fs.statSync(path.join(sessoesPath, file)).isDirectory()
            );

            let sessoesLimpas = 0;

            for (const sessionId of sessionDirs) {
                const sessionPath = path.join(sessoesPath, sessionId);
                const stats = fs.statSync(sessionPath);
                const ageDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
                
                // Limpar sessões com mais de 7 dias sem atividade
                if (ageDays > 7) {
                    fs.rmSync(sessionPath, { recursive: true, force: true });
                    sessoesLimpas++;
                    this.log('🗑️', `Sessão órfã removida: ${sessionId}`);
                }
            }

            if (sessoesLimpas > 0) {
                this.sucesso(`${sessoesLimpas} sessões órfãs removidas`);
            } else {
                this.sucesso('Nenhuma sessão órfã encontrada');
            }
            
        } catch (error) {
            this.erro(`Erro ao limpar sessões órfãs: ${error.message}`);
        }
    }

    // 6. REINICIAR PM2
    async reiniciarPM2() {
        this.log('🔄', 'Reiniciando PM2...');
        
        try {
            // Parar todos os processos
            await execAsync('pm2 stop all');
            this.log('⏹️', 'Processos PM2 parados');
            
            // Aguardar 2 segundos
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Iniciar todos os processos
            await execAsync('pm2 start all');
            this.sucesso('Processos PM2 reiniciados');
            
            // Verificar status
            const { stdout } = await execAsync('pm2 list');
            console.log('📊 Status PM2 após reinício:');
            console.log(stdout);
            
        } catch (error) {
            this.erro(`Erro ao reiniciar PM2: ${error.message}`);
        }
    }

    // 7. VERIFICAR APLICAÇÃO
    async verificarAplicacao() {
        this.log('🔍', 'Verificando aplicação após correções...');
        
        try {
            // Aguardar aplicação inicializar
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Verificar logs recentes
            const { stdout } = await execAsync('pm2 logs --lines 10');
            
            if (stdout.includes('408') || stdout.includes('timeout')) {
                this.erro('Ainda há erros 408 nos logs');
            } else {
                this.sucesso('Nenhum erro 408 detectado nos logs recentes');
            }
            
        } catch (error) {
            this.erro(`Erro ao verificar aplicação: ${error.message}`);
        }
    }

    // RELATÓRIO FINAL
    gerarRelatorio() {
        console.log('\n' + '=' .repeat(50));
        console.log('📊 RELATÓRIO DE CORREÇÕES APLICADAS');
        console.log('=' .repeat(50));
        
        if (this.corrcoesAplicadas.length > 0) {
            this.log('✅', `${this.corrcoesAplicadas.length} correção(ões) aplicada(s):`);
            this.corrcoesAplicadas.forEach((correcao, index) => {
                console.log(`   ${index + 1}. ${correcao}`);
            });
        }

        if (this.erros.length > 0) {
            this.log('❌', `${this.erros.length} erro(s) encontrado(s):`);
            this.erros.forEach((erro, index) => {
                console.log(`   ${index + 1}. ${erro}`);
            });
        }

        console.log('\n💾 Backup dos arquivos originais em:', this.backupDir);
        console.log('\n📋 PRÓXIMOS PASSOS:');
        console.log('   1. Monitorar logs: pm2 logs --follow');
        console.log('   2. Testar criação de novos dispositivos');
        console.log('   3. Verificar se dispositivos banidos desconectam corretamente');
        console.log('   4. Monitorar aleatoriedade do sistema');
        
        console.log('\n⏰ Correções concluídas em:', new Date().toLocaleString());
    }

    // MÉTODO PRINCIPAL
    async aplicar() {
        try {
            await this.criarBackup();
            await this.corrigirSessionValidator();
            await this.verificarDependencias();
            await this.atualizarEnv();
            await this.limparSessoesOrfas();
            await this.reiniciarPM2();
            await this.verificarAplicacao();
            
            this.gerarRelatorio();
            
        } catch (error) {
            console.error('❌ Erro crítico durante aplicação das correções:', error.message);
            console.error('🔍 Stack trace:', error.stack);
            
            console.log('\n🔄 Para reverter as mudanças, execute:');
            console.log(`   cp -r ${this.backupDir}/* ./`);
        }
    }
}

// EXECUTAR CORREÇÕES
const correcao = new CorrecaoProducao();
correcao.aplicar();
