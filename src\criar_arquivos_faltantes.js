/**
 * SCRIPT PARA CRIAR ARQUIVOS FALTANTES NA PRODUÇÃO
 * Baseado no diagnóstico executado
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 CRIANDO ARQUIVOS FALTANTES NA PRODUÇÃO');
console.log('=' .repeat(50));

// 1. CRIAR/ATUALIZAR .ENV
function atualizarEnv() {
    console.log('⚙️ Atualizando arquivo .env...');
    
    const envPath = path.join(__dirname, '.env');
    let envContent = '';
    
    // Ler conteúdo existente se houver
    if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Adicionar variáveis necessárias se não existirem
    const variaveisNecessarias = [
        'LARAVEL_BASE_URL=http://localhost',
        'REQUEST_TIMEOUT=30000',
        'RETRY_ATTEMPTS=3',
        'RETRY_DELAY=2000',
        'SESSION_TIMEOUT=300000'
    ];
    
    let atualizado = false;
    
    variaveisNecessarias.forEach(variavel => {
        const [chave] = variavel.split('=');
        if (!envContent.includes(chave)) {
            envContent += `\n${variavel}`;
            atualizado = true;
            console.log(`✅ Adicionada: ${chave}`);
        }
    });
    
    if (atualizado) {
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Arquivo .env atualizado');
    } else {
        console.log('ℹ️ .env já contém as variáveis necessárias');
    }
}

// 2. CRIAR DIRETÓRIO MIDDLEWARE
function criarDiretorioMiddleware() {
    console.log('📁 Criando diretório middleware...');
    
    const middlewareDir = path.join(__dirname, 'middleware');
    
    if (!fs.existsSync(middlewareDir)) {
        fs.mkdirSync(middlewareDir, { recursive: true });
        console.log('✅ Diretório middleware criado');
    } else {
        console.log('ℹ️ Diretório middleware já existe');
    }
}

// 3. CRIAR SESSIONVALIDATOR.JS
function criarSessionValidator() {
    console.log('🔧 Criando sessionValidator.js...');
    
    const validatorPath = path.join(__dirname, 'middleware', 'sessionValidator.js');
    
    const sessionValidatorCode = `/**
 * SESSION VALIDATOR - CORRIGE ERRO 408 (REQUEST TIMEOUT)
 */
import axios from 'axios';

class SessionValidator {
    constructor() {
        this.laravelBaseUrl = process.env.LARAVEL_BASE_URL || 'http://localhost';
        this.timeout = parseInt(process.env.REQUEST_TIMEOUT) || 30000;
        this.retryAttempts = parseInt(process.env.RETRY_ATTEMPTS) || 3;
        this.retryDelay = parseInt(process.env.RETRY_DELAY) || 2000;
    }

    async middleware(req, res, next) {
        try {
            const sessionId = req.params.sessionId || req.body.sessionId;
            
            if (!sessionId) {
                return res.status(400).json({
                    error: 'Session ID é obrigatório',
                    code: 'MISSING_SESSION_ID'
                });
            }

            const isValid = await this.validateSessionWithRetry(sessionId);
            
            if (!isValid) {
                return res.status(401).json({
                    error: 'Sessão inválida ou expirada',
                    code: 'INVALID_SESSION',
                    sessionId: sessionId
                });
            }

            req.session = {
                id: sessionId,
                validated: true,
                timestamp: new Date().toISOString()
            };

            next();

        } catch (error) {
            console.error('❌ Session Validator Error:', {
                error: error.message,
                timestamp: new Date().toISOString()
            });

            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                return res.status(408).json({
                    error: 'Request Timeout - Falha na comunicação',
                    code: 'REQUEST_TIMEOUT'
                });
            }

            return res.status(500).json({
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            });
        }
    }

    async validateSessionWithRetry(sessionId, attempt = 1) {
        try {
            console.log(\`🔍 Validando sessão \${sessionId} (tentativa \${attempt}/\${this.retryAttempts})\`);
            
            const response = await axios.get(\`\${this.laravelBaseUrl}/api/whatsapp/session/\${sessionId}/validate\`, {
                timeout: this.timeout,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            return response.status === 200 && response.data.valid;

        } catch (error) {
            if (attempt < this.retryAttempts && this.isRetryableError(error)) {
                await this.sleep(this.retryDelay);
                return this.validateSessionWithRetry(sessionId, attempt + 1);
            }

            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                throw error;
            }

            return false;
        }
    }

    isRetryableError(error) {
        const retryableCodes = ['ECONNABORTED', 'ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED'];
        const retryableStatuses = [408, 429, 502, 503, 504];

        return retryableCodes.includes(error.code) || 
               retryableStatuses.includes(error.response?.status);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async updateDeviceStatus(sessionId, status, reason = null) {
        try {
            console.log(\`🔄 Atualizando status do dispositivo \${sessionId} para: \${status}\`);
            
            const response = await axios.post(\`\${this.laravelBaseUrl}/api/whatsapp/device/\${sessionId}/status\`, {
                status: status,
                reason: reason,
                timestamp: new Date().toISOString(),
                source: 'node_session_validator'
            }, {
                timeout: this.timeout,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            return response.status === 200;

        } catch (error) {
            console.error(\`❌ Erro ao atualizar status do dispositivo \${sessionId}:\`, error.message);
            return false;
        }
    }
}

const sessionValidator = new SessionValidator();
export default sessionValidator;
export { SessionValidator };
`;

    fs.writeFileSync(validatorPath, sessionValidatorCode);
    console.log('✅ sessionValidator.js criado');
}

// 4. CRIAR DIRETÓRIO SESSIONS
function criarDiretorioSessions() {
    console.log('📁 Criando diretório sessions...');
    
    const sessionsDir = path.join(__dirname, 'sessions');
    
    if (!fs.existsSync(sessionsDir)) {
        fs.mkdirSync(sessionsDir, { recursive: true });
        console.log('✅ Diretório sessions criado');
    } else {
        console.log('ℹ️ Diretório sessions já existe');
    }
}

// 5. VERIFICAR/INSTALAR AXIOS
async function verificarAxios() {
    console.log('📦 Verificando axios...');
    
    try {
        const packageJsonPath = path.join(__dirname, 'package.json');
        
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            if (!packageJson.dependencies?.axios) {
                console.log('📦 Axios não encontrado, será necessário instalar');
                console.log('🔧 Execute: npm install axios');
            } else {
                console.log('✅ Axios já está instalado');
            }
        } else {
            console.log('⚠️ package.json não encontrado');
        }
    } catch (error) {
        console.error('❌ Erro ao verificar axios:', error.message);
    }
}

// EXECUTAR TODAS AS CORREÇÕES
async function executar() {
    try {
        console.log('🚀 Iniciando criação de arquivos faltantes...\n');
        
        atualizarEnv();
        console.log('');
        
        criarDiretorioMiddleware();
        console.log('');
        
        criarSessionValidator();
        console.log('');
        
        criarDiretorioSessions();
        console.log('');
        
        await verificarAxios();
        
        console.log('\n' + '=' .repeat(50));
        console.log('✅ ARQUIVOS CRIADOS COM SUCESSO!');
        console.log('=' .repeat(50));
        console.log('📋 PRÓXIMOS PASSOS:');
        console.log('   1. npm install axios (se necessário)');
        console.log('   2. Configurar LARAVEL_BASE_URL no .env');
        console.log('   3. pm2 restart all');
        console.log('   4. pm2 logs --follow');
        console.log('\n⏰ Concluído em:', new Date().toLocaleString());
        
    } catch (error) {
        console.error('❌ Erro durante execução:', error.message);
    }
}

executar();
