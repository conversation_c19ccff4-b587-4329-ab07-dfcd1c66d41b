/**
 * CORREÇÃO DO SESSION VALIDATOR - RESOLVER ERRO 408 (REQUEST TIMEOUT)
 * 
 * Problemas identificados nos logs PM2:
 * - Request Timeout (408)
 * - Session Validator Error
 * - Falha na comunicação Node.js ↔ <PERSON><PERSON>
 */

import axios from 'axios';

class SessionValidator {
    constructor() {
        this.laravelBaseUrl = process.env.LARAVEL_BASE_URL || 'http://localhost';
        this.timeout = 30000; // 30 segundos
        this.retryAttempts = 3;
        this.retryDelay = 2000; // 2 segundos
    }

    /**
     * Middleware para validar sessões com tratamento de timeout
     */
    async middleware(req, res, next) {
        try {
            const sessionId = req.params.sessionId || req.body.sessionId;
            
            if (!sessionId) {
                return res.status(400).json({
                    error: 'Session ID é obrigatório',
                    code: 'MISSING_SESSION_ID'
                });
            }

            // Validar sessão com retry e timeout
            const isValid = await this.validateSessionWithRetry(sessionId);
            
            if (!isValid) {
                return res.status(401).json({
                    error: 'Sessão inválida ou expirada',
                    code: 'INVALID_SESSION',
                    sessionId: sessionId
                });
            }

            // Adicionar dados da sessão ao request
            req.session = {
                id: sessionId,
                validated: true,
                timestamp: new Date().toISOString()
            };

            next();

        } catch (error) {
            console.error('❌ Session Validator Error:', {
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });

            // Tratamento específico para erro 408
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                return res.status(408).json({
                    error: 'Request Timeout - Falha na comunicação com Laravel',
                    code: 'REQUEST_TIMEOUT',
                    details: 'Verifique a conectividade com o servidor Laravel'
                });
            }

            return res.status(500).json({
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR',
                message: error.message
            });
        }
    }

    /**
     * Validar sessão com retry automático
     */
    async validateSessionWithRetry(sessionId, attempt = 1) {
        try {
            console.log(`🔍 Validando sessão ${sessionId} (tentativa ${attempt}/${this.retryAttempts})`);
            
            const response = await axios.get(`${this.laravelBaseUrl}/api/whatsapp/session/${sessionId}/validate`, {
                timeout: this.timeout,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': 'Node.js WhatsApp Validator'
                },
                // Configurações para evitar problemas de conectividade
                maxRedirects: 3,
                validateStatus: (status) => status < 500 // Não falhar em 4xx
            });

            if (response.status === 200 && response.data.valid) {
                console.log(`✅ Sessão ${sessionId} validada com sucesso`);
                return true;
            }

            console.log(`❌ Sessão ${sessionId} inválida:`, response.data);
            return false;

        } catch (error) {
            console.error(`❌ Erro na validação (tentativa ${attempt}):`, {
                sessionId,
                error: error.message,
                code: error.code,
                response: error.response?.status
            });

            // Se não é o último attempt e é um erro temporário, tentar novamente
            if (attempt < this.retryAttempts && this.isRetryableError(error)) {
                console.log(`🔄 Aguardando ${this.retryDelay}ms antes da próxima tentativa...`);
                await this.sleep(this.retryDelay);
                return this.validateSessionWithRetry(sessionId, attempt + 1);
            }

            // Se é erro de timeout ou conexão, throw para tratamento específico
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                throw error;
            }

            // Para outros erros, considerar sessão inválida
            return false;
        }
    }

    /**
     * Verificar se o erro permite retry
     */
    isRetryableError(error) {
        const retryableCodes = [
            'ECONNABORTED', // Timeout
            'ECONNRESET',   // Conexão resetada
            'ENOTFOUND',    // DNS temporário
            'ECONNREFUSED'  // Conexão recusada (servidor temporariamente indisponível)
        ];

        const retryableStatuses = [408, 429, 502, 503, 504];

        return retryableCodes.includes(error.code) || 
               retryableStatuses.includes(error.response?.status);
    }

    /**
     * Sleep helper
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Atualizar status do dispositivo no Laravel
     */
    async updateDeviceStatus(sessionId, status, reason = null) {
        try {
            console.log(`🔄 Atualizando status do dispositivo ${sessionId} para: ${status}`);
            
            const response = await axios.post(`${this.laravelBaseUrl}/api/whatsapp/device/${sessionId}/status`, {
                status: status,
                reason: reason,
                timestamp: new Date().toISOString(),
                source: 'node_session_validator'
            }, {
                timeout: this.timeout,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (response.status === 200) {
                console.log(`✅ Status do dispositivo ${sessionId} atualizado com sucesso`);
                return true;
            }

            console.error(`❌ Falha ao atualizar status do dispositivo ${sessionId}:`, response.data);
            return false;

        } catch (error) {
            console.error(`❌ Erro ao atualizar status do dispositivo ${sessionId}:`, {
                error: error.message,
                code: error.code,
                response: error.response?.status
            });
            return false;
        }
    }

    /**
     * Notificar dispositivo banido
     */
    async notifyDeviceBanned(sessionId, reason = 'Device banned by WhatsApp') {
        try {
            console.log(`🚫 Notificando ban do dispositivo ${sessionId}`);
            
            // Atualizar status no Laravel
            await this.updateDeviceStatus(sessionId, 'banned', reason);
            
            // Limpar sessão local se existir
            await this.clearLocalSession(sessionId);
            
            console.log(`✅ Dispositivo ${sessionId} marcado como banido`);
            return true;

        } catch (error) {
            console.error(`❌ Erro ao notificar ban do dispositivo ${sessionId}:`, error.message);
            return false;
        }
    }

    /**
     * Limpar sessão local
     */
    async clearLocalSession(sessionId) {
        try {
            const sessionPath = `./sessions/${sessionId}`;
            const fs = await import('fs');
            const path = await import('path');
            
            if (fs.existsSync(sessionPath)) {
                fs.rmSync(sessionPath, { recursive: true, force: true });
                console.log(`🗑️ Sessão local ${sessionId} removida`);
            }
            
        } catch (error) {
            console.error(`❌ Erro ao limpar sessão local ${sessionId}:`, error.message);
        }
    }
}

// Exportar instância singleton
const sessionValidator = new SessionValidator();

export default sessionValidator;
export { SessionValidator };

/**
 * Middleware para uso direto em rotas Express
 * 
 * Uso:
 * import sessionValidator from './middleware/sessionValidator.js';
 * app.use('/api/whatsapp/:sessionId', sessionValidator.middleware.bind(sessionValidator));
 */
