/**
 * DIAGNÓSTICO FINAL PARA PRODUÇÃO - FOCO NOS PROBLEMAS DOS LOGS PM2
 * 
 * Problemas identificados:
 * - Request Timeout (408) no sessionValidator
 * - Dispositivos banidos não desconectam
 * - Baileys Socket Error
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 DIAGNÓSTICO PRODUÇÃO - FOCO EM PROBLEMAS CRÍTICOS');
console.log('=' .repeat(60));
console.log('📅 Data:', new Date().toLocaleString());
console.log('');

class DiagnosticoCritico {
    constructor() {
        this.problemas = [];
        this.solucoes = [];
    }

    log(emoji, mensagem) {
        console.log(`${emoji} ${mensagem}`);
    }

    problema(descricao, solucao = null) {
        this.problemas.push(descricao);
        if (solucao) this.solucoes.push(solucao);
        this.log('❌', descricao);
        if (solucao) this.log('🔧', `Solução: ${solucao}`);
    }

    sucesso(mensagem) {
        this.log('✅', mensagem);
    }

    // 1. VERIFICAR SESSIONVALIDATOR.JS (CAUSA DO ERRO 408)
    async verificarSessionValidator() {
        this.log('🔍', 'Verificando sessionValidator.js...');
        
        const validatorPath = path.join(__dirname, 'middleware', 'sessionValidator.js');
        
        if (!fs.existsSync(validatorPath)) {
            this.problema(
                'sessionValidator.js não encontrado',
                'Criar arquivo middleware/sessionValidator.js'
            );
            return;
        }

        try {
            const content = fs.readFileSync(validatorPath, 'utf8');
            
            // Verificar se tem timeout configurado
            if (!content.includes('timeout')) {
                this.problema(
                    'sessionValidator sem configuração de timeout',
                    'Adicionar timeout nas requisições HTTP'
                );
            }

            // Verificar se trata erro 408
            if (!content.includes('408') && !content.includes('timeout')) {
                this.problema(
                    'sessionValidator não trata erro 408',
                    'Implementar tratamento para Request Timeout'
                );
            }

            this.sucesso('sessionValidator.js encontrado');
            
        } catch (error) {
            this.problema(
                `Erro ao ler sessionValidator: ${error.message}`,
                'Verificar permissões do arquivo'
            );
        }
    }

    // 2. VERIFICAR PROCESSOS PM2
    async verificarProcessos() {
        this.log('🔍', 'Verificando processos PM2...');
        
        try {
            const { stdout } = await execAsync('pm2 list');
            
            if (stdout.includes('errored') || stdout.includes('stopped')) {
                this.problema(
                    'Processos PM2 com erro ou parados',
                    'Executar pm2 restart all'
                );
            } else {
                this.sucesso('Processos PM2 rodando normalmente');
            }
            
            console.log('📊 Status PM2:');
            console.log(stdout);
            
        } catch (error) {
            this.problema(
                `Erro ao verificar PM2: ${error.message}`,
                'Verificar se PM2 está instalado'
            );
        }
    }

    // 3. VERIFICAR LOGS RECENTES
    async verificarLogs() {
        this.log('🔍', 'Verificando logs recentes...');
        
        try {
            const { stdout } = await execAsync('pm2 logs --lines 20');
            
            const logs = stdout.toLowerCase();
            
            if (logs.includes('408') || logs.includes('timeout')) {
                this.problema(
                    'Erros 408 (Request Timeout) detectados nos logs',
                    'Corrigir sessionValidator.js com timeout apropriado'
                );
            }

            if (logs.includes('baileys') && logs.includes('error')) {
                this.problema(
                    'Erros do Baileys detectados',
                    'Verificar configuração WebSocket do Baileys'
                );
            }

            if (logs.includes('session') && logs.includes('error')) {
                this.problema(
                    'Erros de sessão detectados',
                    'Verificar comunicação Node.js ↔ Laravel'
                );
            }

            this.sucesso('Análise de logs concluída');
            
        } catch (error) {
            this.problema(
                `Erro ao verificar logs: ${error.message}`,
                'Verificar se PM2 tem logs disponíveis'
            );
        }
    }

    // 4. VERIFICAR DIRETÓRIO DE SESSÕES
    async verificarSessoes() {
        this.log('🔍', 'Verificando sessões WhatsApp...');
        
        const sessoesPath = path.join(__dirname, 'sessions');
        
        if (!fs.existsSync(sessoesPath)) {
            this.problema(
                'Diretório sessions não encontrado',
                'Criar diretório sessions'
            );
            return;
        }

        try {
            const files = fs.readdirSync(sessoesPath);
            const sessionDirs = files.filter(file => 
                fs.statSync(path.join(sessoesPath, file)).isDirectory()
            );

            if (sessionDirs.length === 0) {
                this.log('⚠️', 'Nenhuma sessão ativa encontrada');
            } else {
                this.sucesso(`${sessionDirs.length} sessões encontradas`);
                
                // Verificar sessões órfãs (sem processo ativo)
                sessionDirs.forEach(sessionId => {
                    const sessionPath = path.join(sessoesPath, sessionId);
                    const stats = fs.statSync(sessionPath);
                    const ageDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
                    
                    if (ageDays > 7) {
                        this.log('⚠️', `Sessão antiga: ${sessionId} (${ageDays.toFixed(1)} dias)`);
                    }
                });
            }
            
        } catch (error) {
            this.problema(
                `Erro ao verificar sessões: ${error.message}`,
                'Verificar permissões do diretório sessions'
            );
        }
    }

    // 5. VERIFICAR ARQUIVO .ENV
    async verificarEnv() {
        this.log('🔍', 'Verificando configurações .env...');
        
        const envPath = path.join(__dirname, '.env');
        
        if (!fs.existsSync(envPath)) {
            this.problema(
                'Arquivo .env não encontrado',
                'Criar arquivo .env com configurações necessárias'
            );
            return;
        }

        try {
            const content = fs.readFileSync(envPath, 'utf8');
            
            const requiredVars = [
                'LARAVEL_BASE_URL',
                'DB_HOST',
                'DB_DATABASE',
                'DB_USERNAME',
                'DB_PASSWORD'
            ];

            for (const varName of requiredVars) {
                if (!content.includes(varName)) {
                    this.problema(
                        `Variável ${varName} não encontrada no .env`,
                        `Adicionar ${varName} ao arquivo .env`
                    );
                }
            }

            this.sucesso('Arquivo .env verificado');
            
        } catch (error) {
            this.problema(
                `Erro ao ler .env: ${error.message}`,
                'Verificar permissões do arquivo .env'
            );
        }
    }

    // 6. TESTAR CONECTIVIDADE BÁSICA
    async testarConectividade() {
        this.log('🔍', 'Testando conectividade básica...');
        
        try {
            // Teste simples de DNS
            const { stdout } = await execAsync('nslookup google.com');
            if (stdout.includes('Non-authoritative answer')) {
                this.sucesso('DNS funcionando');
            } else {
                this.problema('Problema com DNS', 'Verificar configuração de rede');
            }
            
        } catch (error) {
            this.problema(
                `Erro de conectividade: ${error.message}`,
                'Verificar conexão de rede'
            );
        }
    }

    // RELATÓRIO FINAL
    gerarRelatorio() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 RELATÓRIO FINAL');
        console.log('=' .repeat(60));
        
        if (this.problemas.length === 0) {
            this.sucesso('Nenhum problema crítico detectado!');
        } else {
            this.log('❌', `${this.problemas.length} problema(s) detectado(s):`);
            
            this.problemas.forEach((problema, index) => {
                console.log(`   ${index + 1}. ${problema}`);
            });
        }

        if (this.solucoes.length > 0) {
            console.log('\n🔧 AÇÕES RECOMENDADAS:');
            this.solucoes.forEach((solucao, index) => {
                console.log(`   ${index + 1}. ${solucao}`);
            });
        }

        console.log('\n📋 PRÓXIMOS PASSOS:');
        console.log('   1. Corrigir sessionValidator.js para evitar erro 408');
        console.log('   2. Implementar sincronização de status de dispositivos');
        console.log('   3. Reiniciar PM2: pm2 restart all');
        console.log('   4. Monitorar logs: pm2 logs --follow');
        
        console.log('\n⏰ Diagnóstico concluído em:', new Date().toLocaleString());
    }

    // MÉTODO PRINCIPAL
    async executar() {
        try {
            await this.verificarEnv();
            await this.verificarSessionValidator();
            await this.verificarProcessos();
            await this.verificarSessoes();
            await this.verificarLogs();
            await this.testarConectividade();
            
            this.gerarRelatorio();
            
        } catch (error) {
            console.error('❌ Erro crítico durante diagnóstico:', error.message);
            console.error('🔍 Stack trace:', error.stack);
        }
    }
}

// EXECUTAR DIAGNÓSTICO
const diagnostico = new DiagnosticoCritico();
diagnostico.executar();
