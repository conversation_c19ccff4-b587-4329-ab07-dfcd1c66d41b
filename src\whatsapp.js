import { rmSync, readdir} from 'fs'
import { join } from 'path'
import pino from 'pino'
import axios from 'axios'
import queryString from 'query-string'
import makeWASocket, {
    useMultiFileAuthState,
    makeInMemoryStore,
    Browsers,
    DisconnectReason,
    delay,
} from '@adiwajshing/baileys'
import { toDataURL } from 'qrcode'
import __dirname from './dirname.js'
import response from './response.js'

const sessions = new Map()
const retries = new Map()

const sessionsDir = (sessionId = '') => {
    return join(__dirname, 'storage/sessions', sessionId ? sessionId : '')
}

const isSessionExists = (sessionId) => {
    return sessions.has(sessionId)
}

const shouldReconnect = (sessionId) => {
    let maxRetries = parseInt(process.env.WA_MAX_RECONNECT_ATTEMPTS ?? process.env.MAX_RETRIES ?? 2)
    let attempts = retries.get(sessionId) ?? 0

    maxRetries = maxRetries < 1 ? 2 : maxRetries

    if (attempts < maxRetries) {
        ++attempts
        console.log(`[${sessionId}] Reconnection attempt ${attempts}/${maxRetries}`)
        retries.set(sessionId, attempts)
        return true
    }
    console.log(`[${sessionId}] Max reconnection attempts reached (${maxRetries})`)
    return false
}

// Enhanced connection timeout handling
const getConnectionTimeout = () => {
    return parseInt(process.env.WA_CONNECTION_TIMEOUT ?? 30000)
}

// Enhanced retry delay with exponential backoff
const getRetryDelay = (attempt = 1) => {
    const baseDelay = parseInt(process.env.WA_RETRY_DELAY ?? 5000)
    return Math.min(baseDelay * Math.pow(2, attempt - 1), 30000) // Max 30 seconds
}


// proxy security && system logic


const xswpNodeSocketSet = async (sessionId, isLegacy = false, host, res = null) => {
    try {
        const sessionFile = (isLegacy ? 'legacy_' : 'md_') + sessionId + (isLegacy ? '.json' : '')
        const logger = pino({ level: 'warn' })
        const store = makeInMemoryStore({ logger })
        let state, saveState
        if (isLegacy) {

        } else {
            ({ state, saveCreds: saveState } = await useMultiFileAuthState(sessionsDir(sessionFile)));
        }
        /**
         * @type {import('@adiwajshing/baileys').CommonSocketConfig}
         */
        const waConfig = {
            auth: state,
            printQRInTerminal: true,
            logger,
            browser: Browsers.macOS('Mac OS'),
            // DESABILITAR INIT QUERIES PARA EVITAR ERROS
            fireInitQueries: false,
            syncFullHistory: false,
            markOnlineOnConnect: false,
        }
        /**
         * @type {import('@adiwajshing/baileys').AnyWASocket}
         */
        const wa = makeWASocket.default(waConfig)
        if (!isLegacy) {
            store.readFromFile(sessionsDir(`${sessionId}_store.json`))
            store.bind(wa.ev)
        }
        sessions.set(sessionId, { ...wa, store, isLegacy })
        wa.ev.on('creds.update', saveState)
        wa.ev.on('chats.set', ({ chats }) => {
            if (isLegacy) {
                store.chats.insertIfAbsent(...chats)
            }
        })

        wa.ev.on('connection.update', async (update) => {
            const { connection, lastDisconnect } = update
            const statusCode = lastDisconnect?.error?.output?.statusCode

            console.log(`[${sessionId}] Connection update:`, { connection, statusCode })

            if (connection === 'open') {
                retries.delete(sessionId)
                console.log(`[${sessionId}] Connection opened successfully`)
            }

            if (connection === 'close') {
                console.log(`[${sessionId}] Connection closed. Status code:`, statusCode)

                // Handle specific disconnect reasons
                if (statusCode === DisconnectReason.loggedOut) {
                    console.log(`[${sessionId}] Device logged out, cleaning up session`)
                    if (res && !res.headersSent) {
                        response(res, 400, false, 'Device logged out.')
                    }
                    return deleteSession(sessionId, isLegacy)
                }

                if (!shouldReconnect(sessionId)) {
                    console.log(`[${sessionId}] Max retries reached, cleaning up session`)
                    if (res && !res.headersSent) {
                        response(res, 400, false, 'Unable to create session.')
                    }
                    return deleteSession(sessionId, isLegacy)
                }

                // Calculate delay based on disconnect reason and retry attempt
                const currentAttempt = retries.get(sessionId) ?? 0
                let delay = 0

                if (statusCode === DisconnectReason.restartRequired) {
                    delay = 1000 // Quick restart for restart required
                } else if (statusCode === 515) {
                    delay = 10000 // 10 segundos para erro 515 (stream error)
                } else if (statusCode === DisconnectReason.connectionClosed) {
                    delay = 5000 // 5 segundos para connection closed
                } else {
                    delay = parseInt(process.env.RECONNECT_INTERVAL ?? 5000)
                }

                console.log(`[${sessionId}] Reconnecting in ${delay}ms...`)
                setTimeout(
                    () => {
                        xswpNodeSocketSet(sessionId, isLegacy, host, res)
                    },
                    delay
                )
            }
            if (update.qr) {
                if (res && !res.headersSent) {
                    try {
                        const qr = await toDataURL(update.qr)
                        response(res, 200, true, 'QR code received, please scan the QR code.', { qr })
                        return
                    } catch {
                        response(res, 400, false, 'Unable to create QR code.')
                    }
                }
                try {
                    await wa.logout()
                } catch {
                } finally {
                    deleteSession(sessionId, isLegacy)
                }
            }
        })
    } catch (error) {
        if (res) {
          res.status(404).json({ error: 'An error occurred'});
        }
    }
}

const createSession = async (sessionId, isLegacy = false, req, res = null) => {
    try {
        const sessionFile = (isLegacy ? 'legacy_' : 'md_') + sessionId + (isLegacy ? '.json' : '')
        const logger = pino({ level: 'warn' })
        const store = makeInMemoryStore({ logger })
        let state, saveState

        if (isLegacy) {
            // Legacy logic here if needed
        } else {
            ({ state, saveCreds: saveState } = await useMultiFileAuthState(sessionsDir(sessionFile)));
        }

        const waConfig = {
            auth: state,
            printQRInTerminal: true,
            logger: false,
            browser: Browsers.macOS('Chrome'),
            // DESABILITAR INIT QUERIES PARA EVITAR ERROS
            fireInitQueries: false,
            syncFullHistory: false,
            markOnlineOnConnect: false,
        }

        const wa = makeWASocket.default(waConfig)

        if (!isLegacy) {
            store.readFromFile(sessionsDir(`${sessionId}_store.json`))
            store.bind(wa.ev)
        }

        sessions.set(sessionId, { ...wa, store, isLegacy })

        wa.ev.on('creds.update', saveState)

        wa.ev.on('chats.set', ({ chats }) => {
            if (isLegacy) {
                store.chats.insertIfAbsent(...chats)
            }
        })

        // Handle connection updates with enhanced error handling
        wa.ev.on('connection.update', (update) => {
            const { connection, lastDisconnect } = update
            const statusCode = lastDisconnect?.error?.output?.statusCode

            console.log(`[${sessionId}] CreateSession - Connection update:`, { connection, statusCode })

            if (connection === 'open') {
                retries.delete(sessionId)
                console.log(`[${sessionId}] CreateSession - Connection opened successfully`)
            }

            if (connection === 'close') {
                console.log(`[${sessionId}] CreateSession - Connection closed. Status code:`, statusCode)

                if (statusCode === DisconnectReason.loggedOut) {
                    console.log(`[${sessionId}] CreateSession - Device logged out, cleaning up`)
                    retries.delete(sessionId)
                    sessions.delete(sessionId)
                    return
                }

                const retryCount = retries.get(sessionId) ?? 0
                const maxRetries = parseInt(process.env.WA_MAX_RECONNECT_ATTEMPTS ?? 3)

                if (retryCount < maxRetries) {
                    retries.set(sessionId, retryCount + 1)
                    const delay = getRetryDelay(retryCount + 1)
                    console.log(`[${sessionId}] CreateSession - Retrying in ${delay}ms (${retryCount + 1}/${maxRetries})`)
                    setTimeout(() => createSession(sessionId, isLegacy, req, res), delay)
                } else {
                    console.log(`[${sessionId}] CreateSession - Max retries reached, cleaning up`)
                    retries.delete(sessionId)
                    sessions.delete(sessionId)
                }
            }
        })

        return wa

    } catch (error) {
        console.error('Error creating session:', error)
        if (res) {
          res.status(400).json({ error: 'An error occurred while creating session' });
        }
        throw error
    }
}

const getSession = (sessionId) => {
    return sessions.get(sessionId) ?? null
}

const deleteSession = (sessionId, isLegacy = false) => {
    const sessionFile = (isLegacy ? 'legacy_' : 'md_') + sessionId + (isLegacy ? '.json' : '')
    const storeFile = `${sessionId}_store.json`
    const rmOptions = { force: true, recursive: true }

    rmSync(sessionsDir(sessionFile), rmOptions)
    rmSync(sessionsDir(storeFile), rmOptions)

    sessions.delete(sessionId)
    retries.delete(sessionId)
}

const getChatList = (sessionId, isGroup = false) => {
    const filter = isGroup ? '@g.us' : '@s.whatsapp.net'

    return getSession(sessionId).store.chats.filter((chat) => {
        return chat.id.endsWith(filter)
    })
}

const isExists = async (session, jid, isGroup = false) => {
    try {
        let result

        if (isGroup) {
            result = await session.groupMetadata(jid)

            return Boolean(result.id)
        }

        if (session.isLegacy) {
            result = await session.onWhatsApp(jid)
        } else {
            ;[result] = await session.onWhatsApp(jid)
        }

        return result.exists
    } catch {
        return false
    }
}

const sendMessage = async (session, receiver, message, delayMs = 2000) => {
    try {
        await delay(parseInt(delayMs))

        return session.sendMessage(receiver, message)
    } catch {
        return Promise.reject(null) // eslint-disable-line prefer-promise-reject-errors
    }
}

const formatPhone = (phone) => {
    if (phone.endsWith('@s.whatsapp.net')) {
        return phone
    }

    let formatted = phone.replace(/\D/g, '')

    return (formatted += '@s.whatsapp.net')
}

const formatGroup = (group) => {
    if (group.endsWith('@g.us')) {
        return group
    }

    let formatted = group.replace(/[^\d-]/g, '')

    return (formatted += '@g.us')
}

const cleanup = () => {
    console.log('Running cleanup before exit.')
    sessions.forEach((session, sessionId) => {
        if (!session.isLegacy) {
            session.store.writeToFile(sessionsDir(`${sessionId}_store.json`))
        }
    })
}

const init = () => {
    console.log('🚀 Initializing WhatsApp sessions...')

    readdir(sessionsDir(), (err, files) => {
        if (err) {
            console.log('Sessions directory not found, creating...')
            return
        }
        for (const file of files) {
            if ((!file.startsWith('md_') && !file.startsWith('legacy_')) || file.endsWith('_store')) {
                continue
            }
            const filename = file.replace('.json', '')
            const isLegacy = filename.split('_', 1)[0] !== 'md'
            const sessionId = filename.substring(isLegacy ? 7 : 3)
            console.log(`Loading session: ${sessionId}`)
            xswpNodeSocketSet(sessionId, isLegacy)
        }
    })

    console.log('✅ WhatsApp service ready')
}

export {
    isSessionExists,
    xswpNodeSocketSet,
    createSession,
    getSession,
    deleteSession,
    getChatList,
    isExists,
    sendMessage,
    formatPhone,
    formatGroup,
    cleanup,
    init,
}
