/**
 * DIAGNÓSTICO SIMPLIFICADO PARA PRODUÇÃO - SEM DEPENDÊNCIAS EXTERNAS
 * 
 * Baseado nos problemas identificados nos logs PM2:
 * - Request Timeout (408) 
 * - Session Validator Error
 * - Baileys Socket Error
 * - Dispositivos banidos não desconectam do painel
 */

import fs from 'fs';
import path from 'path';
import http from 'http';
import https from 'https';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 DIAGNÓSTICO SIMPLIFICADO - PRODUÇÃO');
console.log('=' .repeat(50));
console.log('📅 Data:', new Date().toLocaleString());
console.log('');

class DiagnosticoProducao {
    constructor() {
        this.problemas = [];
        this.solucoes = [];
        this.laravelBaseUrl = null;
        this.configs = {};
    }

    async iniciar() {
        console.log('🔄 Iniciando diagnóstico simplificado...\n');
        
        try {
            await this.verificarArquivosEsseniciais();
            await this.analisarConfiguracoes();
            await this.verificarProcessos();
            await this.verificarSessoes();
            await this.verificarMiddleware();
            await this.testarEndpoints();
            await this.verificarLogs();
            
            this.gerarRelatorioSimples();
            
        } catch (error) {
            console.error('❌ Erro durante diagnóstico:', error.message);
        }
    }

    async verificarArquivosEsseniciais() {
        console.log('📁 Verificando arquivos essenciais...');
        
        const arquivos = [
            { path: '.env', critico: true },
            { path: 'app.js', critico: true },
            { path: 'wp/middlewares/sessionValidator.js', critico: true },
            { path: 'package.json', critico: false },
            { path: 'sessions', critico: false, isDir: true }
        ];
        
        for (const arquivo of arquivos) {
            const caminhoCompleto = path.join(process.cwd(), arquivo.path);
            const existe = fs.existsSync(caminhoCompleto);
            
            if (existe) {
                if (arquivo.isDir) {
                    const stats = fs.statSync(caminhoCompleto);
                    console.log(`  ✅ ${arquivo.path}/ - Diretório encontrado`);
                } else {
                    console.log(`  ✅ ${arquivo.path} - Encontrado`);
                }
            } else {
                const status = arquivo.critico ? '❌' : '⚠️';
                console.log(`  ${status} ${arquivo.path} - ${arquivo.critico ? 'CRÍTICO' : 'OPCIONAL'} - Não encontrado`);
                
                if (arquivo.critico) {
                    this.problemas.push(`Arquivo crítico ${arquivo.path} não encontrado`);
                }
            }
        }
        console.log('');
    }

    async analisarConfiguracoes() {
        console.log('⚙️ Analisando configurações...');
        
        // Verificar .env
        const envPath = path.join(process.cwd(), '.env');
        if (fs.existsSync(envPath)) {
            try {
                const conteudo = fs.readFileSync(envPath, 'utf8');
                const linhas = conteudo.split('\n');
                
                const configsImportantes = [
                    'APP_URL',
                    'NODE_API_URL', 
                    'DB_HOST',
                    'DB_DATABASE'
                ];
                
                linhas.forEach(linha => {
                    const [chave, valor] = linha.split('=');
                    if (chave && configsImportantes.includes(chave.trim())) {
                        this.configs[chave.trim()] = valor ? valor.trim() : '';
                    }
                });
                
                if (this.configs.APP_URL) {
                    this.laravelBaseUrl = this.configs.APP_URL;
                    console.log(`  ✅ Laravel URL: ${this.laravelBaseUrl}`);
                } else {
                    console.log('  ❌ APP_URL não configurada');
                    this.problemas.push('APP_URL não configurada no .env');
                }
                
                if (this.configs.DB_HOST) {
                    console.log(`  ✅ Database Host: ${this.configs.DB_HOST}`);
                } else {
                    console.log('  ❌ DB_HOST não configurada');
                    this.problemas.push('Configuração de banco não encontrada');
                }
                
            } catch (error) {
                console.log(`  ❌ Erro ao ler .env: ${error.message}`);
                this.problemas.push('Erro ao ler arquivo .env');
            }
        }
        console.log('');
    }

    async verificarProcessos() {
        console.log('🔄 Verificando processos...');
        
        try {
            // Verificar se PM2 está rodando
            const { stdout: pm2List } = await execAsync('pm2 list').catch(() => ({ stdout: '' }));
            
            if (pm2List.includes('app.js') || pm2List.includes('node')) {
                console.log('  ✅ Processo Node.js encontrado no PM2');
                
                // Extrair informações básicas
                const linhas = pm2List.split('\n');
                linhas.forEach(linha => {
                    if (linha.includes('app.js') || linha.includes('node')) {
                        console.log(`    📊 ${linha.trim()}`);
                    }
                });
                
            } else {
                console.log('  ❌ Processo Node.js não encontrado no PM2');
                this.problemas.push('Aplicação Node.js não está rodando no PM2');
            }
            
        } catch (error) {
            console.log(`  ⚠️ Não foi possível verificar PM2: ${error.message}`);
        }
        console.log('');
    }

    async verificarSessoes() {
        console.log('📱 Verificando sessões WhatsApp...');
        
        const diretorioSessoes = path.join(process.cwd(), 'sessions');
        
        if (fs.existsSync(diretorioSessoes)) {
            const arquivos = fs.readdirSync(diretorioSessoes);
            const sessoes = arquivos.filter(arquivo => 
                arquivo.endsWith('.json') || 
                arquivo.endsWith('.session') ||
                fs.statSync(path.join(diretorioSessoes, arquivo)).isDirectory()
            );
            
            console.log(`  📂 Total de sessões: ${sessoes.length}`);
            
            if (sessoes.length > 0) {
                console.log('  📋 Status das sessões:');
                
                sessoes.slice(0, 10).forEach(sessao => { // Mostrar apenas 10 primeiras
                    const caminhoSessao = path.join(diretorioSessoes, sessao);
                    const stats = fs.statSync(caminhoSessao);
                    const ultimaModificacao = stats.mtime;
                    const agora = new Date();
                    const diferencaMinutos = Math.floor((agora - ultimaModificacao) / (1000 * 60));
                    
                    const status = diferencaMinutos > 30 ? '🔴' : diferencaMinutos > 10 ? '🟡' : '🟢';
                    console.log(`    ${status} ${sessao} - ${diferencaMinutos} min atrás`);
                    
                    if (diferencaMinutos > 60) {
                        this.problemas.push(`Sessão ${sessao} inativa há mais de 1 hora`);
                    }
                });
                
                if (sessoes.length > 10) {
                    console.log(`    ... e mais ${sessoes.length - 10} sessões`);
                }
            }
            
        } else {
            console.log('  ❌ Diretório de sessões não encontrado');
            this.problemas.push('Diretório de sessões não encontrado');
        }
        console.log('');
    }

    async verificarMiddleware() {
        console.log('🔧 Analisando middleware sessionValidator...');
        
        const middlewarePath = path.join(process.cwd(), 'wp/middlewares/sessionValidator.js');
        
        if (fs.existsSync(middlewarePath)) {
            try {
                const conteudo = fs.readFileSync(middlewarePath, 'utf8');
                
                // Verificar problemas conhecidos que causam timeout 408
                const verificacoes = [
                    {
                        regex: /timeout.*:\s*(\d+)/i,
                        nome: 'Configuração de timeout',
                        acao: (match) => {
                            const timeout = parseInt(match[1]);
                            if (timeout < 10000) {
                                this.problemas.push(`Timeout muito baixo: ${timeout}ms - pode causar erro 408`);
                                this.solucoes.push('Aumentar timeout para pelo menos 30000ms');
                            }
                            return `Timeout configurado: ${timeout}ms`;
                        }
                    },
                    {
                        regex: /res\.status\(408\)/,
                        nome: 'Retorno de erro 408',
                        acao: () => {
                            this.problemas.push('Middleware retorna erro 408 - verificar lógica');
                            this.solucoes.push('Revisar condições que geram erro 408');
                            return 'Encontrado retorno de erro 408';
                        }
                    },
                    {
                        regex: /boom\.clientTimeout/i,
                        nome: 'Uso do Boom para timeout',
                        acao: () => {
                            this.problemas.push('Uso inadequado do Boom para timeout');
                            this.solucoes.push('Corrigir tratamento de timeout no middleware');
                            return 'Uso do Boom para timeout encontrado';
                        }
                    }
                ];
                
                verificacoes.forEach(verificacao => {
                    const match = conteudo.match(verificacao.regex);
                    if (match) {
                        const resultado = verificacao.acao(match);
                        console.log(`  ⚠️ ${verificacao.nome}: ${resultado}`);
                    } else {
                        console.log(`  ✅ ${verificacao.nome}: OK`);
                    }
                });
                
                // Verificar tamanho do arquivo (muito grande pode indicar lógica complexa)
                const tamanhoKB = Math.round(conteudo.length / 1024);
                console.log(`  📊 Tamanho do middleware: ${tamanhoKB}KB`);
                
                if (tamanhoKB > 50) {
                    this.problemas.push('Middleware muito grande - pode causar lentidão');
                    this.solucoes.push('Otimizar lógica do middleware');
                }
                
            } catch (error) {
                console.log(`  ❌ Erro ao analisar middleware: ${error.message}`);
                this.problemas.push('Erro ao analisar middleware');
            }
        } else {
            console.log('  ❌ Middleware não encontrado');
            this.problemas.push('Middleware sessionValidator.js não encontrado');
        }
        console.log('');
    }

    async testarEndpoints() {
        console.log('🌐 Testando endpoints críticos...');
        
        if (!this.laravelBaseUrl) {
            console.log('  ⚠️ URL do Laravel não configurada - pulando testes');
            return;
        }
        
        const endpoints = [
            { path: '/', nome: 'Home Page' },
            { path: '/api/health', nome: 'Health Check' },
            { path: '/api/whatsapp/status', nome: 'WhatsApp Status' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                const url = this.laravelBaseUrl + endpoint.path;
                const resultado = await this.testarHttpRequest(url, 5000);
                
                const status = resultado.statusCode || 'N/A';
                if (resultado.success) {
                    console.log(`  ✅ ${endpoint.nome} - Status: ${status}`);
                } else {
                    const emoji = status === 408 ? '🔴' : '⚠️';
                    console.log(`  ${emoji} ${endpoint.nome} - ${status}: ${resultado.error}`);
                    
                    if (status === 408) {
                        this.problemas.push(`Timeout (408) em ${endpoint.nome} - problema crítico`);
                        this.solucoes.push('Corrigir middleware sessionValidator.js');
                    }
                }
                
            } catch (error) {
                console.log(`  ❌ ${endpoint.nome} - Exceção: ${error.message}`);
            }
        }
        console.log('');
    }

    async testarHttpRequest(url, timeout = 5000) {
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            const modulo = urlObj.protocol === 'https:' ? https : http;
            
            const req = modulo.get(url, { timeout }, (res) => {
                resolve({
                    success: res.statusCode < 400,
                    statusCode: res.statusCode
                });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({
                    success: false,
                    error: 'Request timeout',
                    statusCode: 408
                });
            });
            
            req.on('error', (error) => {
                resolve({
                    success: false,
                    error: error.message,
                    statusCode: null
                });
            });
        });
    }

    async verificarLogs() {
        console.log('📋 Verificando logs recentes...');
        
        try {
            // Tentar verificar logs do PM2
            const { stdout: pm2Logs } = await execAsync('pm2 logs --lines 20 --raw').catch(() => ({ stdout: '' }));
            
            if (pm2Logs) {
                const errosComuns = [
                    'Request Time-out',
                    'Timeout',
                    'ECONNRESET',
                    'socket hang up',
                    'Baileys',
                    '408'
                ];
                
                const linhasLog = pm2Logs.split('\n').slice(-20); // Últimas 20 linhas
                const errosEncontrados = new Set();
                
                linhasLog.forEach(linha => {
                    errosComuns.forEach(erro => {
                        if (linha.toLowerCase().includes(erro.toLowerCase())) {
                            errosEncontrados.add(erro);
                        }
                    });
                });
                
                if (errosEncontrados.size > 0) {
                    console.log('  ⚠️ Erros encontrados nos logs:');
                    errosEncontrados.forEach(erro => {
                        console.log(`    - ${erro}`);
                        this.problemas.push(`Erro nos logs: ${erro}`);
                    });
                } else {
                    console.log('  ✅ Nenhum erro crítico nos logs recentes');
                }
            }
            
        } catch (error) {
            console.log(`  ⚠️ Não foi possível verificar logs: ${error.message}`);
        }
        console.log('');
    }

    gerarRelatorioSimples() {
        console.log('📊 RELATÓRIO DIAGNÓSTICO SIMPLIFICADO');
        console.log('=' .repeat(50));
        
        console.log('\n🔴 PROBLEMAS ENCONTRADOS:');
        if (this.problemas.length === 0) {
            console.log('  ✅ Nenhum problema crítico detectado');
        } else {
            this.problemas.forEach((problema, index) => {
                console.log(`  ${index + 1}. ${problema}`);
            });
        }
        
        console.log('\n💡 AÇÕES RECOMENDADAS:');
        const solucoesUnicas = [...new Set(this.solucoes)];
        
        // Adicionar soluções padrão baseadas nos problemas dos logs PM2
        const acoesObrigatorias = [
            'Corrigir middleware sessionValidator.js para resolver erro 408',
            'Sincronizar status dos dispositivos banidos com o banco de dados',
            'Implementar limpeza automática de sessões inativas',
            'Aumentar timeout de requisições para evitar erro 408'
        ];
        
        const todasSolucoes = [...solucoesUnicas, ...acoesObrigatorias];
        const solucoesFinais = [...new Set(todasSolucoes)];
        
        solucoesFinais.forEach((solucao, index) => {
            console.log(`  ${index + 1}. ${solucao}`);
        });
        
        console.log('\n🎯 PRÓXIMOS PASSOS CRÍTICOS:');
        console.log('  1. 🔧 Aplicar correção do middleware sessionValidator.js');
        console.log('  2. 🔄 Implementar sincronização de status de dispositivos');
        console.log('  3. 🚀 Reiniciar PM2 após correções');
        console.log('  4. 📊 Monitorar logs por 30 minutos');
        
        console.log('\n⚡ COMANDOS PARA EXECUÇÃO:');
        console.log('  pm2 restart all');
        console.log('  pm2 logs --lines 50');
        console.log('  pm2 monit');
        
        console.log('\n📅 Diagnóstico concluído:', new Date().toLocaleString());
        console.log('🔥 Execute este diagnóstico novamente após aplicar as correções');
    }
}

// Executar diagnóstico
const diagnostico = new DiagnosticoProducao();
diagnostico.iniciar().catch(console.error);
