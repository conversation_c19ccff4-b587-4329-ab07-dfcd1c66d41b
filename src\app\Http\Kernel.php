<?php

namespace App\Http;

use App\Http\Middleware\DomainVerificationMiddleware;
use App\Http\Middleware\HandleActiveTabMiddleware;
use App\Http\Middleware\PurchaseValidation;
use App\Http\Middleware\SoftwareVerification;
use App\Http\Middleware\VerifyDomain;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{

    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Fruitcake\Cors\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,

    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\LanguageMiddleware::class,
            // SoftwareVerification::class, // Removido para desabilitar verificação de domínio
            // PurchaseValidation::class // Removido para desabilitar verificação de domínio
        ],

        'api' => [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,

        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth'               => \App\Http\Middleware\Authenticate::class,
        'auth.basic'         => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session'       => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers'      => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can'                => \Illuminate\Auth\Middleware\Authorize::class,
        'guest'              => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm'   => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed'             => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle'           => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified'           => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        // 'upgrade'            => \App\Http\Middleware\VersionUpgrade::class, // DESABILITADO TEMPORARIAMENTE
        'admin'              => \App\Http\Middleware\IfNotCheckAdmin::class,
        'admin.guest'        => \App\Http\Middleware\IfCheckAdmin::class,
        'allow.access'       => \App\Http\Middleware\AccessMiddleware::class,
        'checkUserStatus'    => \App\Http\Middleware\CheckUserStatus::class,
        'registration'          => \App\Http\Middleware\RegistrationAllow::class,
        'login'                 => \App\Http\Middleware\LoginAllow::class,
        'maintenance'        => \App\Http\Middleware\MaintenanceMode::class,
        'redirect.to.login'  => \App\Http\Middleware\RedirectToLogin::class,
        'demo.mode'          => \App\Http\Middleware\DemoMode::class,
        'incoming.api'       => \App\Http\Middleware\IncomingApiMiddleware::class,
        'authorization'      => \App\Http\Middleware\AuthorizationMiddleware::class,
        'sanitizer'          => \App\Http\Middleware\Sanitization::class,
        'sms.api.gateway.access' => \App\Http\Middleware\SmsApiGatewayAccessMiddleware::class,
        'email.gateway.access'   => \App\Http\Middleware\EmailGatewayAccessMiddleware::class,
        'android.gateway.token' => \App\Http\Middleware\AndroidGatewayToken::class,
        'queue.auth' => \App\Http\Middleware\QueueAuthMiddleware::class,
        'demo.restrict' => \App\Http\Middleware\RestrictDemoMode::class,
        'domain.verified'  => VerifyDomain::class,
        'check.domain'     => DomainVerificationMiddleware::class,
    ];
}
