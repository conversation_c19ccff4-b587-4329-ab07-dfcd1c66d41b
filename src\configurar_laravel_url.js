/**
 * CONFIGURAR LARAVEL_BASE_URL AUTOMATICAMENTE
 * Detecta automaticamente a URL base do Laravel na produção
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 CONFIGURANDO LARAVEL_BASE_URL AUTOMATICAMENTE');
console.log('=' .repeat(50));

function detectarLaravelBaseUrl() {
    console.log('🔍 Detectando URL base do Laravel...');
    
    // URLs possíveis baseadas na estrutura do servidor
    const urlsCandidatas = [
        'https://mconnect.uniqsuporte.com.br',
        'http://mconnect.uniqsuporte.com.br',
        'http://localhost:8000',
        'http://localhost',
        'http://127.0.0.1'
    ];
    
    // Verificar se há alguma pista no diretório atual
    const caminhoAtual = __dirname;
    
    if (caminhoAtual.includes('mconnect.uniqsuporte.com.br')) {
        console.log('✅ Detectado domínio: mconnect.uniqsuporte.com.br');
        return 'https://mconnect.uniqsuporte.com.br';
    }
    
    // Fallback
    console.log('⚠️ Usando URL padrão');
    return 'http://localhost';
}

function atualizarEnvComUrl() {
    const envPath = path.join(__dirname, '.env');
    const laravelUrl = detectarLaravelBaseUrl();
    
    console.log(\`🔧 Configurando LARAVEL_BASE_URL: \${laravelUrl}\`);
    
    let envContent = '';
    
    // Ler arquivo existente
    if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Verificar se LARAVEL_BASE_URL já existe
    if (envContent.includes('LARAVEL_BASE_URL=')) {
        // Substituir valor existente
        envContent = envContent.replace(
            /LARAVEL_BASE_URL=.*/g,
            \`LARAVEL_BASE_URL=\${laravelUrl}\`
        );
        console.log('✅ LARAVEL_BASE_URL atualizada');
    } else {
        // Adicionar nova variável
        envContent += \`\nLARAVEL_BASE_URL=\${laravelUrl}\`;
        console.log('✅ LARAVEL_BASE_URL adicionada');
    }
    
    // Adicionar outras variáveis necessárias
    const variaveisExtras = [
        'REQUEST_TIMEOUT=30000',
        'RETRY_ATTEMPTS=3',
        'RETRY_DELAY=2000',
        'SESSION_TIMEOUT=300000',
        'NODE_ENV=production'
    ];
    
    variaveisExtras.forEach(variavel => {
        const [chave] = variavel.split('=');
        if (!envContent.includes(chave + '=')) {
            envContent += \`\n\${variavel}\`;
            console.log(\`✅ Adicionada: \${chave}\`);
        }
    });
    
    // Salvar arquivo
    fs.writeFileSync(envPath, envContent);
    console.log('💾 Arquivo .env salvo');
    
    return laravelUrl;
}

function mostrarConfiguracaoFinal() {
    console.log('\n' + '=' .repeat(50));
    console.log('📋 CONFIGURAÇÃO FINAL');
    console.log('=' .repeat(50));
    
    const envPath = path.join(__dirname, '.env');
    
    if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const linhas = envContent.split('\n');
        
        console.log('📄 Variáveis configuradas:');
        linhas.forEach(linha => {
            if (linha.trim() && !linha.startsWith('#')) {
                console.log(\`   \${linha}\`);
            }
        });
    }
    
    console.log('\n🔧 PRÓXIMOS PASSOS:');
    console.log('   1. Verificar se a URL está correta');
    console.log('   2. Testar conectividade: curl https://mconnect.uniqsuporte.com.br');
    console.log('   3. Executar: node criar_arquivos_faltantes.js');
    console.log('   4. pm2 restart all');
}

// EXECUTAR CONFIGURAÇÃO
try {
    const url = atualizarEnvComUrl();
    mostrarConfiguracaoFinal();
    
    console.log(\`\n✅ LARAVEL_BASE_URL configurada para: \${url}\`);
    console.log('⏰ Configuração concluída em:', new Date().toLocaleString());
    
} catch (error) {
    console.error('❌ Erro durante configuração:', error.message);
}
