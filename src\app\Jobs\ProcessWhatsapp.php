<?php

namespace App\Jobs;

use App\Enums\CommunicationStatusEnum;
use App\Enums\StatusEnum;
use App\Http\Utility\SendWhatsapp;
use App\Models\CommunicationLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessWhatsapp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CommunicationLog $whatsappLog){}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $whatsappLog = $this->whatsappLog;
            $gateway = $whatsappLog->whatsappGateway;

            // APLICAR DELAYS ANTI-BAN BASEADOS NA CONFIGURAÇÃO DO GATEWAY
            if ($gateway && $gateway->credentials) {
                $credentials = $gateway->credentials;

                // 1. Delay básico entre mensagens (min_delay e max_delay)
                $minDelay = $credentials['min_delay'] ?? 15;
                $maxDelay = $credentials['max_delay'] ?? 30;
                $basicDelay = rand($minDelay * 1000, $maxDelay * 1000) / 1000; // Converter para segundos com decimais

                Log::info("WhatsApp Delay Applied", [
                    'gateway' => $gateway->name,
                    'basic_delay' => $basicDelay,
                    'min_delay' => $minDelay,
                    'max_delay' => $maxDelay
                ]);

                sleep($basicDelay);

                // 2. Delay adicional após X mensagens (delay_after_count e delay_after_duration)
                $delayAfterCount = $credentials['delay_after_count'] ?? 50;
                $delayAfterDuration = $credentials['delay_after_duration'] ?? 300;

                // Simular contador de mensagens (usar ID do log como aproximação)
                if ($delayAfterCount > 0 && $delayAfterDuration > 0) {
                    $messageCount = $whatsappLog->id % $delayAfterCount;
                    if ($messageCount === 0) {
                        \Log::info("WhatsApp Extended Delay Applied", [
                            'gateway' => $gateway->name,
                            'extended_delay' => $delayAfterDuration,
                            'after_count' => $delayAfterCount
                        ]);
                        sleep($delayAfterDuration);
                    }
                }
            }

            if($whatsappLog->whatsappGateway->type == StatusEnum::FALSE->status() && $whatsappLog->status != CommunicationStatusEnum::FAIL->value) {

                SendWhatsapp::sendNodeMessages($whatsappLog, null);

            } else {

                SendWhatsapp::sendCloudApiMessages($whatsappLog, null);
            }
        } catch (\Exception $exception) {

            \Log::error("Process whatsapp failed: " . $exception->getMessage());
        }
    }
}
