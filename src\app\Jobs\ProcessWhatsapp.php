<?php

namespace App\Jobs;

use App\Enums\CommunicationStatusEnum;
use App\Enums\StatusEnum;
use App\Http\Utility\SendWhatsapp;
use App\Models\CommunicationLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessWhatsapp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CommunicationLog $whatsappLog){}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            \Log::info("ProcessWhatsapp INICIADO", ['log_id' => $this->whatsappLog->id]);

            $whatsappLog = $this->whatsappLog;
            $gateway = $whatsappLog->whatsappGateway;

            \Log::info("ProcessWhatsapp Gateway Info", [
                'gateway_id' => $gateway ? $gateway->id : 'null',
                'gateway_name' => $gateway ? $gateway->name : 'null',
                'gateway_type' => $gateway ? $gateway->type : 'null'
            ]);

            // APLICAR DELAYS AVANÇADOS PARA WHATSAPP
            if ($gateway) {
                $this->applyWhatsAppDelays($gateway);
            }

            if($whatsappLog->whatsappGateway->type == StatusEnum::FALSE->status() && $whatsappLog->status != CommunicationStatusEnum::FAIL->value) {

                \Log::info("ProcessWhatsapp: Enviando via Node.js");
                SendWhatsapp::sendNodeMessages($whatsappLog, null);

            } else {

                \Log::info("ProcessWhatsapp: Enviando via Cloud API");
                SendWhatsapp::sendCloudApiMessages($whatsappLog, null);
            }

            \Log::info("ProcessWhatsapp FINALIZADO", ['log_id' => $this->whatsappLog->id]);
        } catch (\Exception $exception) {

            \Log::error("Process whatsapp failed: " . $exception->getMessage());
        }
    }

    /**
     * Aplicar delays avançados para WhatsApp
     */
    protected function applyWhatsAppDelays($gateway): void
    {
        $currentCount = $gateway->sent_message_count ?? 0;
        $newCount = $currentCount + 1;

        // 1. DELAY BÁSICO (per_message_min_delay e per_message_max_delay)
        if ($gateway->per_message_min_delay > 0 || $gateway->per_message_max_delay > 0) {
            $minDelay = max(0, $gateway->per_message_min_delay);
            $maxDelay = max($minDelay, $gateway->per_message_max_delay);
            $delay = mt_rand((int)($minDelay * 1_000_000), (int)($maxDelay * 1_000_000));

            \Log::info("WhatsApp Basic Delay Applied", [
                'gateway' => $gateway->name,
                'delay_microseconds' => $delay,
                'min_delay' => $minDelay,
                'max_delay' => $maxDelay
            ]);

            usleep($delay);
        }

        // 2. DELAY APÓS CONTAGEM (delay_after_count e delay_after_duration)
        if ($gateway->delay_after_count > 0 && $newCount >= $gateway->delay_after_count) {
            $cycles = floor($newCount / $gateway->delay_after_count) - floor($currentCount / $gateway->delay_after_count);

            if ($gateway->delay_after_duration > 0 && $cycles > 0) {
                $cycleDelay = (int)($gateway->delay_after_duration * 1_000_000 * $cycles);
                \Log::info("WhatsApp Cycle Delay Applied", [
                    'gateway' => $gateway->name,
                    'cycle_delay_microseconds' => $cycleDelay,
                    'cycles' => $cycles,
                    'delay_after_count' => $gateway->delay_after_count,
                    'delay_after_duration' => $gateway->delay_after_duration
                ]);

                usleep($cycleDelay);
            }
        }

        // 3. RESET APÓS CONTAGEM (reset_after_count)
        if ($gateway->reset_after_count > 0 && $newCount >= $gateway->reset_after_count) {
            $oldCount = $newCount;
            $newCount = $newCount % $gateway->reset_after_count;
            \Log::info("WhatsApp Counter Reset Applied", [
                'gateway' => $gateway->name,
                'old_count' => $oldCount,
                'new_count' => $newCount,
                'reset_after_count' => $gateway->reset_after_count
            ]);
        }

        // 4. ATUALIZAR CONTADOR
        $gateway->sent_message_count = $newCount;
        $gateway->save();

        \Log::info("WhatsApp Gateway Counter Updated", [
            'gateway' => $gateway->name,
            'previous_count' => $currentCount,
            'new_count' => $newCount
        ]);
    }
}
