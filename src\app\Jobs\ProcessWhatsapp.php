<?php

namespace App\Jobs;

use App\Enums\CommunicationStatusEnum;
use App\Enums\StatusEnum;
use App\Http\Utility\SendWhatsapp;
use App\Models\CommunicationLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessWhatsapp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CommunicationLog $whatsappLog){}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $whatsappLog = $this->whatsappLog;
            $gateway = $whatsappLog->whatsappGateway;

            // APLICAR DELAYS ANTI-BAN COMPLETOS (ESTRUTURA DA PASTA 33)
            if ($gateway && $gateway->credentials) {
                $this->updateAndApplyGatewayDelays($gateway, 1);
            }

            if($whatsappLog->whatsappGateway->type == StatusEnum::FALSE->status() && $whatsappLog->status != CommunicationStatusEnum::FAIL->value) {

                SendWhatsapp::sendNodeMessages($whatsappLog, null);

            } else {

                SendWhatsapp::sendCloudApiMessages($whatsappLog, null);
            }
        } catch (\Exception $exception) {

            Log::error("Process whatsapp failed: " . $exception->getMessage());
        }
    }

    /**
     * updateAndApplyGatewayDelays - ESTRUTURA COMPLETA DA PASTA 33
     *
     * @param Gateway $gateway
     * @param int $messagesSent
     *
     * @return void
     */
    protected function updateAndApplyGatewayDelays($gateway, int $messagesSent): void
    {
        $currentCount = $gateway->sent_message_count ?? 0;
        $newCount = $currentCount + $messagesSent;

        // 1. DELAY BÁSICO ENTRE MENSAGENS (per_message_min_delay e per_message_max_delay)
        if ($gateway->per_message_min_delay > 0 || $gateway->per_message_max_delay > 0) {
            $minDelay = max(0, $gateway->per_message_min_delay);
            $maxDelay = max($minDelay, $gateway->per_message_max_delay);

            $totalDelay = 0;
            for ($i = 0; $i < $messagesSent; $i++) {
                $delay = mt_rand((int)($minDelay * 1_000_000), (int)($maxDelay * 1_000_000));
                $totalDelay += $delay;
            }

            Log::info("WhatsApp Basic Delay Applied", [
                'gateway' => $gateway->name,
                'total_delay_microseconds' => $totalDelay,
                'min_delay' => $minDelay,
                'max_delay' => $maxDelay
            ]);

            $start = microtime(true);
            $chunkDelay = 1_000_000;
            while ($totalDelay > 0) {
                $sleepTime = min($chunkDelay, $totalDelay);
                usleep($sleepTime);
                $totalDelay -= $sleepTime;
            }
            $end = microtime(true);
            Log::info("Finished basic delay, actual duration: " . (($end - $start) * 1000000) . " microseconds");
        }

        // 2. DELAY APÓS CONTAGEM (delay_after_count e delay_after_duration)
        if ($gateway->delay_after_count > 0 && $newCount >= $gateway->delay_after_count) {
            $cycles = floor($newCount / $gateway->delay_after_count) - floor($currentCount / $gateway->delay_after_count);

            if ($gateway->delay_after_duration > 0 && $cycles > 0) {
                $cycleDelay = (int)($gateway->delay_after_duration * 1_000_000 * $cycles);
                Log::info("WhatsApp Cycle Delay Applied", [
                    'gateway' => $gateway->name,
                    'cycle_delay_microseconds' => $cycleDelay,
                    'cycles' => $cycles,
                    'delay_after_count' => $gateway->delay_after_count,
                    'delay_after_duration' => $gateway->delay_after_duration
                ]);

                $start = microtime(true);
                usleep($cycleDelay);
                $end = microtime(true);
                Log::info("Finished cycle delay, actual duration: " . (($end - $start) * 1000000) . " microseconds");
            }
        }

        // 3. RESET APÓS CONTAGEM (reset_after_count)
        if ($gateway->reset_after_count > 0 && $newCount >= $gateway->reset_after_count) {
            $oldCount = $newCount;
            $newCount = $newCount % $gateway->reset_after_count;
            Log::info("WhatsApp Counter Reset Applied", [
                'gateway' => $gateway->name,
                'old_count' => $oldCount,
                'new_count' => $newCount,
                'reset_after_count' => $gateway->reset_after_count
            ]);
        }

        // 4. ATUALIZAR CONTADOR NO GATEWAY
        $gateway->sent_message_count = $newCount;
        $gateway->save();

        Log::info("WhatsApp Gateway Counter Updated", [
            'gateway' => $gateway->name,
            'previous_count' => $currentCount,
            'new_count' => $newCount,
            'messages_sent' => $messagesSent
        ]);
    }
}
