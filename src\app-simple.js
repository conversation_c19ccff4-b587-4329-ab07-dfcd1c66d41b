import 'dotenv/config'
import express from 'express'
import cors from 'cors'
import routes from './routes.js'

const app = express()
const host = process.env.NODE_SERVER_HOST || undefined
const port = parseInt(process.env.NODE_SERVER_PORT ?? 3008)

app.use(cors())
app.use(express.urlencoded({ extended: true }))
app.use(express.json())
app.use('/', routes)

const listenerCallback = () => {
    // NÃO inicializar WhatsApp por enquanto
    console.log('🚀 Simple server ready - WhatsApp disabled for testing')
    const actualHost = host || '0.0.0.0'
    console.log(`Server is listening on http://${actualHost}:${port}`)
}

if (host) {
    app.listen(port, host, listenerCallback)
} else {
    app.listen(port, listenerCallback)
}

export default app
